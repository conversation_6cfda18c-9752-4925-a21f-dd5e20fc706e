package startguidance

type CartInfoReq struct {
	LockerNo string `json:"locker_no" form:"locker_no" binding:"required,gte=0"` // LockerNo
}

type TeeTimePersonalReq struct {
	LockerNo string `json:"locker_no" form:"locker_no" binding:"required,gte=0"` // LockerNo
}

type TeeTimeReq struct {
	From  string `json:"from" form:"from" binding:"len=5"`   // from HH:MM
	Limit int    `json:"limit" form:"limit" binding:"gte=0"` //  limit >0
}
