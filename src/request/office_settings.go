package request

// OfficeSettingsCreateReq create request
type OfficeSettingsCreateReq struct {
	// セルフスコア印刷機能　0:無効　1:有効
	EnableSelfScorePrint   *int    `json:"enable_self_score_print"  binding:"required,min=0,max=1"`
	// アンケート機能　0:無効　1:有効
	EnableQuestionnaire    *int    `json:"enable_questionnaire"  binding:"required,min=0,max=1"`
	// スタート案内機能　0:無効　1:有効
	EnableStartGuide       *int    `json:"enable_start_guide"  binding:"required,min=0,max=1"`
	// カードリード種類　0:なし　1:バーコードリード　2:ICカードリード　
	CardReaderType         *int    `json:"card_reader_type"  binding:"required,min=0,max=2"`
	// バーコードからロッカーキーに変換用の正規表現
	BarcodeToLockerRegex   *string   `json:"barcode_to_locker_regex"  binding:"required"`
}

// BarcodeToLockerReq request
type BarcodeToLockerReq struct {
	// バーコード内容
	Barcode     string     `json:"barcode" form:"barcode" binding:"required"`
}

