package repository

import (
	"mi-restful-api/configs"
	"mi-restful-api/enum"
	"mi-restful-api/exception"
	"mi-restful-api/model"
	"net/http"

	"github.com/gin-gonic/gin"
)

// GetOfficeId TODO get office_id
func GetOfficeId(c *gin.Context) (string, error) {
	switch autoDetectedClientType(c) {
	case enum.ClientTypeApp:
		appUserInfo, err := GetAppAuthInfo(c)
		if err != nil {
			return "", err
		}
		return appUserInfo.OfficeID, nil
	case enum.ClientTypeWeb:
		appAuthInfo, err := GetWebAuthInfo(c)
		if err != nil {
			return "", err
		} else {
			return appAuthInfo.OfficeID, nil
		}
	default: //unkown ClientType
		return "", exception.NewError(http.StatusUnauthorized, "unkown client type")
	}
}

func GetOfficeKey(c *gin.Context) (string, error) {
	switch autoDetectedClientType(c) {
	case enum.ClientTypeApp:
		appUserInfo, err := GetAppAuthInfo(c)
		if err != nil {
			return "", err
		}
		return appUserInfo.OfficeKey, nil
	case enum.ClientTypeWeb:
		appAuthInfo, err := GetWebAuthInfo(c)
		if err != nil {
			return "", err
		} else {
			return appAuthInfo.OfficeKey, nil
		}
	default: //unkown ClientType
		return "", exception.NewError(http.StatusUnauthorized, "unkown client type")
	}
}

func autoDetectedClientType(c *gin.Context) string {
	value, exists := c.Get("client_type")
	if !exists {
		return enum.ClientTypeWeb
	}

	clientType, ok := value.(string)
	if !ok {
		return enum.ClientTypeWeb
	}

	// user app auth set client_type
	return clientType
}

func GetAppAuthInfo(c *gin.Context) (*model.AppAuthInfo, error) {
	value, exists := c.Get("app_auth_info")
	if !exists {
		return nil, exception.NewError(http.StatusUnauthorized, "auth info not found")
	}

	userInfo, ok := value.(*model.AppAuthInfo)
	if !ok {
		return nil, exception.NewError(http.StatusInternalServerError, "auth info assert error")
	}

	// return userInfo
	return userInfo, nil
}

func GetWebAuthInfo(c *gin.Context) (*model.AppAuthInfo, error) {
	value, exists := c.Get("office_id")
	if !exists {
		return nil, exception.NewError(http.StatusUnauthorized, "auth info office_id not found")
	}

	officeId, ok := value.(string)
	if !ok {
		return nil, exception.NewError(http.StatusInternalServerError, "auth info officeId assert error")
	}

	officeKeyValue, exists := c.Get("office_key")
	if !exists {
		return nil, exception.NewError(http.StatusUnauthorized, "auth info office_key not found")
	}

	officeKey, ok := officeKeyValue.(string)
	if !ok {
		return nil, exception.NewError(http.StatusInternalServerError, "auth info officeKey assert error")
	}

	return &model.AppAuthInfo{
		OfficeID:  officeId,
		OfficeKey: officeKey,
	}, nil
}

func GetMockAuthInfo(c *gin.Context) (*model.AppAuthInfo, error) {
	return &model.AppAuthInfo{
		OfficeID:  configs.GetMockConfig().OfficeId,
		OfficeKey: configs.GetMockConfig().OfficeKey,
		DeviceID:  enum.MockAuthToken,
	}, nil
}
