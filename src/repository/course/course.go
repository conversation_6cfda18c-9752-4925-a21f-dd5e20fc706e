package repocourse

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"mi-restful-api/client"
	"mi-restful-api/model/course"
	"strconv"
	"time"

	"gorm.io/gorm"
)

type CourseRepos struct {
	ConnMySQLMncdb *gorm.DB
}

func (s *CourseRepos) ListCourse(officeId string) ([]course.Course, error) {
	// Check cache first
	redis := client.GetAppRedisClient()
	cacheKey := fmt.Sprintf("courses_%s", officeId)

	// Try to get from cache
	if cachedData, err := redis.Get(context.Background(), cacheKey).Result(); err == nil {
		var courses []course.Course
		if err := json.Unmarshal([]byte(cachedData), &courses); err == nil {
			slog.Debug("CourseRepos.ListCourse cache hit", "officeId", officeId, "count", len(courses))
			return courses, nil
		}
		// If unmarshal fails, continue to DB query
		slog.Warn("Failed to unmarshal cached courses", "err", err)
	}

	// Cache miss or error, query from database
	var data []course.Course

	courseData := []course.CourseSetting{}
	resp := s.ConnMySQLMncdb.Model(&course.CourseSetting{}).
		Where("office_id=? and deleted_at is null", officeId).
		Order("course_index ASC").
		Find(&courseData)
	if resp.Error != nil {
		slog.Error("get course_setting error", "error", resp.Error)
		return []course.Course{}, resp.Error
	}

	for _, d := range courseData {
		var holeData []course.HoleSetting
		var holes []course.Hole

		resp = s.ConnMySQLMncdb.Model(&course.HoleSetting{}).
			Where("office_id=? and course_index=? and deleted_at is null", officeId, d.CourseIndex).
			Order("course_index ASC").
			Find(&holeData)
		if resp.Error != nil {
			continue
		}

		for _, h := range holeData {
			holes = append(holes, course.Hole{
				GreenIndex: strconv.Itoa(h.GreenIndex),
				HoleIndex:  strconv.Itoa(h.HoleIndex),
				UsedHdcp:   strconv.Itoa(h.Hdcp),
				UsedPar:    strconv.Itoa(h.Par),
			})
		}

		data = append(data, course.Course{
			CourseIndex: strconv.Itoa(d.CourseIndex),
			CourseName:  d.CourseName,
			StartHole:   strconv.Itoa(d.StartHoleNo),
			Holes:       holes,
		})
	}

	// Cache the result for 1 minute
	jsonData, err := json.Marshal(data)
	if err != nil {
		slog.Error("Failed to marshal courses for cache", "err", err)
		// Return the data even if caching fails
		return data, nil
	}

	err = redis.Set(context.Background(), cacheKey, jsonData, 1*time.Minute).Err()
	if err != nil {
		slog.Error("Failed to save courses to cache", "err", err)
		// Return the data even if caching fails
	} else {
		slog.Debug("CourseRepos.ListCourse cached", "officeId", officeId, "count", len(data))
	}

	return data, nil
}

func (s *CourseRepos) ListCoursesWithoutHole(officeId string) ([]course.Course, error) {

	var data []course.Course

	courseData := []course.CourseSetting{}
	resp := s.ConnMySQLMncdb.Model(&course.CourseSetting{}).
		Where("office_id=? and deleted_at is null", officeId).
		Order("course_index ASC").
		Find(&courseData)
	if resp.Error != nil {
		slog.Error("get course_setting error", "err", resp.Error)
		return []course.Course{}, resp.Error
	}

	for _, d := range courseData {
		data = append(data, course.Course{
			CourseIndex: strconv.Itoa(d.CourseIndex),
			CourseName:  d.CourseName,
			StartHole:   strconv.Itoa(d.StartHoleNo),
		})
	}

	return data, nil
}
