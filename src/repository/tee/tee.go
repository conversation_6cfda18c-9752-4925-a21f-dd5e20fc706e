package repotee

import (
	"log/slog"
	"mi-restful-api/configs"
	"mi-restful-api/model/tee"
	"mi-restful-api/utils/dynamo"

	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
)

func TeeInfo(officeKey string) (*tee.OfficeTeeInfo, error) {

	slog.Info("tee repo TeeInfo", "officeKey", officeKey, "tableName", configs.GetDynamodbCompeConfig().MashiaiTableName)

	key := map[string]types.AttributeValue{
		"partition_key": &types.AttributeValueMemberS{Value: officeKey},
		"sort_key":      &types.AttributeValueMemberS{Value: "tee-setting"},
	}

	result, err := dynamo.GetItem(configs.GetDynamodbCompeConfig().MashiaiTableName, key)
	if err != nil {
		slog.Error("operation get tee info", "err", err, "params", key)
		return nil, err
	}
	if result.Item == nil {
		slog.Error("operation get tee info", "err", "no items", "params", key)
		return nil, nil
	}

	var officeTeeInfo tee.OfficeTeeInfo

	attributevalue.UnmarshalMap(result.Item, &officeTeeInfo)

	return &officeTeeInfo, nil
}
