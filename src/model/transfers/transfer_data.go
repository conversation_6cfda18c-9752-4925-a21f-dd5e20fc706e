package transfers

type TransferData struct {
	Version  TransferDataVersion  `json:"version,omitempty"`
	Common   TransferDataCommon   `json:"common"`
	Receiver TransferDataReceiver `json:"receiver"`
	Sender   TransferDataSender   `json:"sender"`
	Details  interface{}          `json:"details"`
}

type TransferDataVersion struct {
	CommonVersion  string `json:"common_version"`
	DetailsVersion string `json:"details_version"`
}

type TransferDataCommon struct {
	DataType     string `json:"data_type"`
	MessageId    string `json:"message_id,omitempty"`
	SendDateTime string `json:"send_date_time"`
}

type TransferDataReceiver struct {
	Version string   `json:"version"`
	Id      []string `json:"id"`
}

type TransferDataSender struct {
	Version   string `json:"version"`
	OfficeKey string `json:"office_key"`
	Id        string `json:"id"`
}
