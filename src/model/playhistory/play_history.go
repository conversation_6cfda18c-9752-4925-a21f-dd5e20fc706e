package playhistory

import "mi-restful-api/model/transfers"

type PlayHistoryDynamoDb struct {
	Partition_key   string                          `dynamodbav:"partition_key"`
	Sort_key        string                          `dynamodbav:"sort_key"`
	Expiration_time int64                           `dynamodbav:"expiration_time"`
	Cart_no         string                          `dynamodbav:"cart_no"`
	Updated_at      string                          `dynamodbav:"updated_at"`
	Header          interface{}                     `dynamodbav:"header"`
	Version         *transfers.TransferDataVersion  `dynamodbav:"version"`
	Sender          *transfers.TransferDataSender   `dynamodbav:"sender"`
	Receiver        *transfers.TransferDataReceiver `dynamodbav:"receiver"`
	Common          *transfers.TransferDataCommon   `dynamodbav:"common"`
	Details         TransferPlayHistoryDetails      `dynamodbav:"details"`
	Players         []PlayHistoryPlayer             `dynamodbav:"players"`
}

type TransferPlayHistoryDetails struct {
	CartNo                   string  `json:"cart_no" dynamodbav:"cart_no"`
	PlayDate                 string  `json:"play_date" dynamodbav:"play_date"`
	PlayCount                string  `json:"play_count" dynamodbav:"play_count"`
	CourseIndex              string  `json:"course_index" dynamodbav:"course_index"`
	StartTime                string  `json:"start_time" dynamodbav:"start_time"`
	StartTimeNew             string  `json:"start_time_new" dynamodbav:"start_time_new"`
	ArrivalTime              string  `json:"arrival_time" dynamodbav:"arrival_time"`
	PlayTime                 string  `json:"play_time" dynamodbav:"play_time"`
	PlayTimeNew              string  `json:"play_time_new" dynamodbav:"play_time_new"`
	DurationHoleNo1Time      string  `json:"duration_hole_no_1_time" dynamodbav:"duration_hole_no_1_time"`
	DurationHoleNo2Time      string  `json:"duration_hole_no_2_time" dynamodbav:"duration_hole_no_2_time"`
	DurationHoleNo3Time      string  `json:"duration_hole_no_3_time" dynamodbav:"duration_hole_no_3_time"`
	DurationHoleNo4Time      string  `json:"duration_hole_no_4_time" dynamodbav:"duration_hole_no_4_time"`
	DurationHoleNo5Time      string  `json:"duration_hole_no_5_time" dynamodbav:"duration_hole_no_5_time"`
	DurationHoleNo6Time      string  `json:"duration_hole_no_6_time" dynamodbav:"duration_hole_no_6_time"`
	DurationHoleNo7Time      string  `json:"duration_hole_no_7_time" dynamodbav:"duration_hole_no_7_time"`
	DurationHoleNo8Time      string  `json:"duration_hole_no_8_time" dynamodbav:"duration_hole_no_8_time"`
	DurationHoleNo9Time      string  `json:"duration_hole_no_9_time" dynamodbav:"duration_hole_no_9_time"`
	IsSelf                   *string `json:"is_self" dynamodbav:"is_self"`
	CaddieNo                 *string `json:"caddie_no" dynamodbav:"caddie_no"`
	DelayTotalTime           string  `json:"delay_total_time" dynamodbav:"delay_total_time"`
	DelayTotalTimeNew        string  `json:"delay_total_time_new" dynamodbav:"delay_total_time_new"`
	ReserveCourseIndex       *string `json:"reserve_course_index" dynamodbav:"reserve_course_index"`
	ReserveTime              *string `json:"reserve_time" dynamodbav:"reserve_time"`
	BookedStartTime          *string `json:"booked_start_time" dynamodbav:"booked_start_time"`
	WaitTime                 string  `json:"wait_time" dynamodbav:"wait_time"`
	DurationHoleNo1WaitTime  string  `json:"duration_hole_no_1_wait_time" dynamodbav:"duration_hole_no_1_wait_time"`
	DurationHoleNo2WaitTime  string  `json:"duration_hole_no_2_wait_time" dynamodbav:"duration_hole_no_2_wait_time"`
	DurationHoleNo3WaitTime  string  `json:"duration_hole_no_3_wait_time" dynamodbav:"duration_hole_no_3_wait_time"`
	DurationHoleNo4WaitTime  string  `json:"duration_hole_no_4_wait_time" dynamodbav:"duration_hole_no_4_wait_time"`
	DurationHoleNo5WaitTime  string  `json:"duration_hole_no_5_wait_time" dynamodbav:"duration_hole_no_5_wait_time"`
	DurationHoleNo6WaitTime  string  `json:"duration_hole_no_6_wait_time" dynamodbav:"duration_hole_no_6_wait_time"`
	DurationHoleNo7WaitTime  string  `json:"duration_hole_no_7_wait_time" dynamodbav:"duration_hole_no_7_wait_time"`
	DurationHoleNo8WaitTime  string  `json:"duration_hole_no_8_wait_time" dynamodbav:"duration_hole_no_8_wait_time"`
	DurationHoleNo9WaitTime  string  `json:"duration_hole_no_9_wait_time" dynamodbav:"duration_hole_no_9_wait_time"`
	DurationHoleNo1DelayTime string  `json:"duration_hole_no_1_delay_time" dynamodbav:"duration_hole_no_1_delay_time"`
	DurationHoleNo2DelayTime string  `json:"duration_hole_no_2_delay_time" dynamodbav:"duration_hole_no_2_delay_time"`
	DurationHoleNo3DelayTime string  `json:"duration_hole_no_3_delay_time" dynamodbav:"duration_hole_no_3_delay_time"`
	DurationHoleNo4DelayTime string  `json:"duration_hole_no_4_delay_time" dynamodbav:"duration_hole_no_4_delay_time"`
	DurationHoleNo5DelayTime string  `json:"duration_hole_no_5_delay_time" dynamodbav:"duration_hole_no_5_delay_time"`
	DurationHoleNo6DelayTime string  `json:"duration_hole_no_6_delay_time" dynamodbav:"duration_hole_no_6_delay_time"`
	DurationHoleNo7DelayTime string  `json:"duration_hole_no_7_delay_time" dynamodbav:"duration_hole_no_7_delay_time"`
	DurationHoleNo8DelayTime string  `json:"duration_hole_no_8_delay_time" dynamodbav:"duration_hole_no_8_delay_time"`
	DurationHoleNo9DelayTime string  `json:"duration_hole_no_9_delay_time" dynamodbav:"duration_hole_no_9_delay_time"`
}

type PlayHistoryPlayer struct {
	PlayerNo   string `json:"player_no" dynamodbav:"player_no"`
	PlayerName string `json:"player_name" dynamodbav:"player_name"`
}
